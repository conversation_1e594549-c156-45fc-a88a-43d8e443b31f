{"name": "vue-migration-tools", "version": "1.0.0", "description": "Vue 2 to Vue 3 migration tools", "main": "index.js", "files": ["index.js", "bin/", "src/", "config/"], "bin": {"vue-migrator": "./bin/vue-migrator.js", "sass-migrator-vue": "./bin/sass-migrator.js", "build-fixer": "./bin/build-fixer.js", "third-party-migrator": "./bin/third-party-migrator.js", "migrate-test-components": "./bin/migrate-test-components.js", "page-validator": "./bin/page-validator.js"}, "scripts": {"migrate": "node index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPatterns=test/.*\\.test\\.js$ --testPathIgnorePatterns=test/e2e/", "test:integration": "jest --testPathPatterns=test/sass/.*\\.test\\.js$", "test:e2e": "jest --testPathPatterns=test/e2e/.*\\.test\\.js$ --runInBand --detectOpenHandles", "test:sass": "jest --testPathPatterns=test/sass/", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src/ test/", "lint:fix": "eslint src/ test/ --fix"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.0", "@vue/eslint-config-standard": "^8.0.1", "ai": "^3.4.33", "axios": "^1.7.7", "commander": "^11.1.0", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.28.0", "fs-extra": "^11.2.0", "glob": "^10.4.5", "glop": "^0.3.1", "gogocode": "^1.0.55", "@unit-mesh/gogocode-plugin-element": "latest", "gogocode-plugin-vue": "latest", "gray-matter": "^4.0.3", "jscodeshift": "^17.3.0", "ora": "^5.4.1", "puppeteer": "^21.5.0", "sass-migrator": "^2.0.0", "semver": "^7.6.3", "express": "^4.18.2", "uuid": "^9.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/jest": "^29.5.0", "eslint": "^8.57.1", "eslint-plugin-jest": "^27.0.0", "jest": "^30.0.0"}}