const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const { AIService } = require('../ai/ai-service');

/**
 * RouteParser - Vue Router 路由解析器
 *
 * 功能：
 * 1. 静态解析 router/ 目录下的路由配置文件
 * 2. 支持 Vue 2 和 Vue 3 的路由格式
 * 3. 当静态解析失败时，使用 AI 分析代码
 * 4. 提取所有可访问的路由路径
 */
class RouteParser extends AIService {
  importedRouterNames = new Set();

  constructor(projectPath, options = {}) {
    super(options);

    this.projectPath = projectPath;
    this.options = {
      verbose: false,
      useAI: true,
      routerPaths: [
        'src/router/index.js',
        'src/router/index.ts',
        'router/index.js',
        'router/index.ts',
        'src/router.js',
        'src/router.ts'
      ],
      ...options
    };

    this.routes = [];
    this.routerFiles = [];
    this.parseErrors = [];
    this.routeComponentMap = new Map(); // 路由路径到组件文件的映射
  }

  /**
   * 解析项目中的所有路由
   */
  async parseRoutes() {
    console.log(chalk.blue('🔍 开始解析项目路由...'));

    try {
      // 1. 查找路由文件
      await this.findRouterFiles();

      if (this.routerFiles.length === 0) {
        throw new Error('未找到路由配置文件');
      }

      // 2. 解析每个路由文件
      for (const routerFile of this.routerFiles) {
        await this.parseRouterFile(routerFile);
      }

      // 3. 如果静态解析失败且启用AI，尝试AI解析
      if (this.routes.length === 0 && this.options.useAI && this.isEnabled()) {
        console.log(chalk.yellow('⚠️  静态解析失败，尝试使用 AI 解析...'));
        await this.parseWithAI();
      }

      console.log(chalk.green(`✅ 路由解析完成，共找到 ${this.routes.length} 个路由`));

      if (this.options.verbose) {
        this.printRoutes();
      }

      return {
        success: true,
        routes: this.routes,
        routerFiles: this.routerFiles,
        errors: this.parseErrors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 路由解析失败: ${error.message}`));
      return {
        success: false,
        routes: [],
        routerFiles: this.routerFiles,
        errors: [...this.parseErrors, error.message]
      };
    }
  }

  /**
   * 查找路由配置文件
   */
  async findRouterFiles() {
    for (const routerPath of this.options.routerPaths) {
      const fullPath = path.join(this.projectPath, routerPath);

      if (await fs.pathExists(fullPath)) {
        this.routerFiles.push({
          path: routerPath,
          fullPath: fullPath
        });

        if (this.options.verbose) {
          console.log(chalk.gray(`   找到路由文件: ${routerPath}`));
        }
      }
    }

    // 递归查找 router 目录下的其他文件
    const routerDir = path.join(this.projectPath, 'src/router');
    if (await fs.pathExists(routerDir)) {
      await this.findAdditionalRouterFiles(routerDir);
    }
  }

  /**
   * 递归查找额外的路由文件
   */
  async findAdditionalRouterFiles(dir) {
    try {
      const files = await fs.readdir(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.stat(filePath);

        if (stat.isDirectory()) {
          await this.findAdditionalRouterFiles(filePath);
        } else if (file.match(/\.(js|ts)$/)) {
          const relativePath = path.relative(this.projectPath, filePath);

          // 避免重复添加已存在的文件
          const exists = this.routerFiles.some(rf => rf.fullPath === filePath);
          if (!exists) {
            this.routerFiles.push({
              path: relativePath,
              fullPath: filePath
            });

            if (this.options.verbose) {
              console.log(chalk.gray(`   找到额外路由文件: ${relativePath}`));
            }
          }
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  读取目录失败 ${dir}: ${error.message}`));
      }
    }
  }

  /**
   * 解析单个路由文件
   */
  async parseRouterFile(routerFile) {
    try {
      const content = await fs.readFile(routerFile.fullPath, 'utf8');

      // 设置当前文件路径，用于解析相对导入
      this.currentFilePath = routerFile.path;

      if (this.options.verbose) {
        console.log(chalk.gray(`   解析文件: ${routerFile.path}`));
      }

      // 尝试静态解析
      const staticRoutes = await this.staticParseRoutes(content, routerFile.path);

      if (staticRoutes.length > 0) {
        this.routes.push(...staticRoutes);
      } else {
        this.parseErrors.push(`静态解析失败: ${routerFile.path}`);
      }

    } catch (error) {
      const errorMsg = `解析文件失败 ${routerFile.path}: ${error.message}`;
      this.parseErrors.push(errorMsg);

      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${errorMsg}`));
      }
    }
  }

  /**
   * 静态解析路由配置
   */
  async staticParseRoutes(content, filePath) {
    try {
      // 使用 Babel 解析 JavaScript/TypeScript
      const ast = parse(content, {
        sourceType: 'module',
        plugins: [
          'jsx',
          'typescript',
          'decorators-legacy',
          'classProperties',
          'objectRestSpread',
          'asyncGenerators',
          'functionBind',
          'exportDefaultFrom',
          'exportNamespaceFrom',
          'dynamicImport'
        ]
      });

      const routes = [];
      const self = this; // 保存 this 上下文

      // 收集导入的路由模块
      const importedRouters = new Map();

      // 遍历 AST 查找路由定义
      traverse(ast, {
        // 收集导入的路由模块
        ImportDeclaration(path) {
          if (path.node.source.value.includes('./modules/') ||
              path.node.source.value.includes('/modules/') ||
              path.node.source.value.includes('router')) {
            for (const specifier of path.node.specifiers) {
              if (specifier.type === 'ImportDefaultSpecifier') {
                importedRouters.set(specifier.local.name, path.node.source.value);
              }
            }
          }
        },

        // 查找 routes 数组定义
        VariableDeclarator(path) {
          if ((path.node.id.name === 'routes' ||
               path.node.id.name === 'constantRoutes' ||
               path.node.id.name === 'asyncRoutes') && path.node.init) {
            const routeArray = self.extractRoutesFromArray(path.node.init, importedRouters);
            routes.push(...routeArray);
          }
        },

        // 查找导出的路由数组
        ExportNamedDeclaration(path) {
          if (path.node.declaration && path.node.declaration.type === 'VariableDeclaration') {
            for (const declarator of path.node.declaration.declarations) {
              if ((declarator.id.name === 'constantRoutes' ||
                   declarator.id.name === 'asyncRoutes' ||
                   declarator.id.name === 'routes') && declarator.init) {
                const routeArray = self.extractRoutesFromArray(declarator.init, importedRouters);
                routes.push(...routeArray);
              }
            }
          }
        },

        // 查找 createRouter 调用
        CallExpression(path) {
          if (path.node.callee.name === 'createRouter' ||
              (path.node.callee.property && path.node.callee.property.name === 'createRouter')) {

            const routesProperty = path.node.arguments[0]?.properties?.find(
              prop => prop.key.name === 'routes'
            );

            if (routesProperty) {
              const routeArray = self.extractRoutesFromArray(routesProperty.value, importedRouters);
              routes.push(...routeArray);
            }
          }
        },

        // 查找 new VueRouter 调用 (Vue 2)
        NewExpression(path) {
          if (path.node.callee.name === 'VueRouter' ||
              (path.node.callee.property && path.node.callee.property.name === 'VueRouter')) {

            const routesProperty = path.node.arguments[0]?.properties?.find(
              prop => prop.key.name === 'routes'
            );

            if (routesProperty) {
              const routeArray = self.extractRoutesFromArray(routesProperty.value, importedRouters);
              routes.push(...routeArray);
            }
          }
        }
      });

      // 解析导入的路由模块
      for (const routerName of this.importedRouterNames) {
        const importPath = importedRouters.get(routerName);
        if (importPath) {
          const importedRoutes = await this.resolveImportedRouter(importPath);
          if (importedRoutes.length > 0) {
            routes.push(...importedRoutes);
            if (this.options.verbose) {
              console.log(chalk.gray(`   解析导入路由 ${routerName}: 找到 ${importedRoutes.length} 个路由`));
            }
          }
        }
      }

      return routes;

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  静态解析失败 ${filePath}: ${error.message}`));
      }
      return [];
    }
  }

  /**
   * 从数组节点中提取路由信息
   */
  extractRoutesFromArray(arrayNode, importedRouters = new Map()) {
    if (!arrayNode || arrayNode.type !== 'ArrayExpression') {
      return [];
    }

    const routes = [];

    for (const element of arrayNode.elements) {
      if (element && element.type === 'ObjectExpression') {
        const route = this.extractRouteFromObject(element);
        if (route) {
          routes.push(route);
        }
      } else if (element && element.type === 'Identifier' && importedRouters.has(element.name)) {
        // 记录需要解析的导入路由模块
        this.importedRouterNames.add(element.name);
        if (this.options.verbose) {
          console.log(chalk.gray(`   发现导入的路由模块: ${element.name} -> ${importedRouters.get(element.name)}`));
        }
      }
    }

    return routes;
  }

  /**
   * 解析导入的路由模块
   */
  async resolveImportedRouter(importPath) {
    try {
      // 解析相对路径
      const currentDir = path.dirname(this.currentFilePath || '');
      let resolvedPath;

      if (importPath.startsWith('./') || importPath.startsWith('../')) {
        resolvedPath = path.resolve(path.join(this.projectPath, currentDir), importPath);
      } else {
        resolvedPath = path.join(this.projectPath, importPath);
      }

      // 尝试添加 .js 扩展名
      if (!resolvedPath.endsWith('.js') && !resolvedPath.endsWith('.ts')) {
        if (await fs.pathExists(resolvedPath + '.js')) {
          resolvedPath += '.js';
        } else if (await fs.pathExists(resolvedPath + '.ts')) {
          resolvedPath += '.ts';
        }
      }

      if (await fs.pathExists(resolvedPath)) {
        // 解析导入的路由文件
        const content = await fs.readFile(resolvedPath, 'utf8');
        const ast = parse(content, {
          sourceType: 'module',
          plugins: [
            'jsx',
            'typescript',
            'decorators-legacy',
            'classProperties',
            'objectRestSpread',
            'asyncGenerators',
            'functionBind',
            'exportDefaultFrom',
            'exportNamespaceFrom',
            'dynamicImport'
          ]
        });

        const routes = [];
        const self = this;

        traverse(ast, {
          // 查找默认导出的路由对象
          ExportDefaultDeclaration(path) {
            if (path.node.declaration.type === 'ObjectExpression') {
              const route = self.extractRouteFromObject(path.node.declaration);
              if (route) {
                routes.push(route);
              }
            } else if (path.node.declaration.type === 'Identifier') {
              // 查找变量定义
              const varName = path.node.declaration.name;
              // 这里需要在同一个文件中查找变量定义
              // 为简化，我们先返回空数组，后续可以完善
            }
          },

          // 查找变量声明中的路由定义
          VariableDeclarator(path) {
            if (path.node.id.name.toLowerCase().includes('router') && path.node.init) {
              if (path.node.init.type === 'ObjectExpression') {
                const route = self.extractRouteFromObject(path.node.init);
                if (route) {
                  routes.push(route);
                }
              }
            }
          }
        });

        return routes;
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  解析导入路由失败 ${importPath}: ${error.message}`));
      }
    }

    return [];
  }

  /**
   * 从对象节点中提取单个路由信息
   */
  extractRouteFromObject(objectNode) {
    const route = {
      path: null,
      name: null,
      component: null,
      meta: {},
      children: [],
      hidden: false,
      redirect: null
    };

    for (const property of objectNode.properties) {
      if (property.key.type === 'Identifier') {
        const key = property.key.name;

        switch (key) {
          case 'path':
            if (property.value.type === 'StringLiteral') {
              route.path = property.value.value;
            }
            break;

          case 'name':
            if (property.value.type === 'StringLiteral') {
              route.name = property.value.value;
            }
            break;

          case 'component':
            route.component = this.extractComponentInfo(property.value);
            break;

          case 'meta':
            if (property.value.type === 'ObjectExpression') {
              route.meta = this.extractMetaInfo(property.value);
            }
            break;

          case 'children':
            if (property.value.type === 'ArrayExpression') {
              route.children = this.extractRoutesFromArray(property.value);
            }
            break;

          case 'hidden':
            if (property.value.type === 'BooleanLiteral') {
              route.hidden = property.value.value;
            }
            break;

          case 'redirect':
            if (property.value.type === 'StringLiteral') {
              route.redirect = property.value.value;
            }
            break;
        }
      }
    }

    // 只返回有效的路由（至少有 path）
    if (route.path) {
      // 建立路由到组件的映射
      this.buildRouteComponentMapping(route);
      return route;
    }
    return null;
  }

  /**
   * 提取组件信息
   */
  extractComponentInfo(valueNode) {
    if (valueNode.type === 'Identifier') {
      return { type: 'imported', name: valueNode.name };
    } else if (valueNode.type === 'ArrowFunctionExpression' || valueNode.type === 'FunctionExpression') {
      // 动态导入组件 - 查找函数体中的 import() 调用
      const importPath = this.findImportInFunction(valueNode);
      if (importPath) {
        return { type: 'dynamic', source: importPath };
      }
      return { type: 'dynamic', source: 'function' };
    } else if (valueNode.type === 'CallExpression') {
      // 处理各种调用表达式
      return this.extractComponentFromCallExpression(valueNode);
    }

    return { type: 'unknown' };
  }

  /**
   * 从调用表达式中提取组件信息
   */
  extractComponentFromCallExpression(callNode) {
    // 直接的 import() 调用
    if (callNode.callee.type === 'Import' && callNode.arguments[0]?.type === 'StringLiteral') {
      return { type: 'dynamic', source: callNode.arguments[0].value };
    }

    // Vue.defineAsyncComponent() 调用
    if (this.isDefineAsyncComponentCall(callNode)) {
      const asyncArg = callNode.arguments[0];
      if (asyncArg) {
        // 处理嵌套的 defineAsyncComponent 调用
        if (asyncArg.type === 'CallExpression' && this.isDefineAsyncComponentCall(asyncArg)) {
          return this.extractComponentFromCallExpression(asyncArg);
        }
        // 处理函数参数
        else if (asyncArg.type === 'ArrowFunctionExpression' || asyncArg.type === 'FunctionExpression') {
          const importPath = this.findImportInFunction(asyncArg);
          if (importPath) {
            return { type: 'async', source: importPath };
          }
        }
        // 处理直接的 import() 调用
        else if (asyncArg.type === 'CallExpression' && asyncArg.callee.type === 'Import') {
          if (asyncArg.arguments[0]?.type === 'StringLiteral') {
            return { type: 'async', source: asyncArg.arguments[0].value };
          }
        }
      }
    }

    return { type: 'unknown' };
  }

  /**
   * 检查是否是 defineAsyncComponent 调用
   */
  isDefineAsyncComponentCall(callNode) {
    if (callNode.callee.type === 'Identifier' && callNode.callee.name === 'defineAsyncComponent') {
      return true;
    }
    if (callNode.callee.type === 'MemberExpression') {
      // Vue.defineAsyncComponent
      if (callNode.callee.object?.name === 'Vue' && callNode.callee.property?.name === 'defineAsyncComponent') {
        return true;
      }
    }
    return false;
  }

  /**
   * 在函数中查找 import() 调用
   */
  findImportInFunction(functionNode) {
    // 检查函数体
    if (functionNode.body) {
      if (functionNode.body.type === 'CallExpression') {
        // 箭头函数直接返回 import() 调用
        if (functionNode.body.callee?.type === 'Import' &&
            functionNode.body.arguments[0]?.type === 'StringLiteral') {
          return functionNode.body.arguments[0].value;
        }
      } else if (functionNode.body.type === 'BlockStatement') {
        // 函数体是代码块，查找 return 语句
        for (const statement of functionNode.body.body) {
          if (statement.type === 'ReturnStatement' &&
              statement.argument?.type === 'CallExpression' &&
              statement.argument.callee?.type === 'Import' &&
              statement.argument.arguments[0]?.type === 'StringLiteral') {
            return statement.argument.arguments[0].value;
          }
        }
      }
    }
    return null;
  }

  /**
   * 提取 meta 信息
   */
  extractMetaInfo(objectNode) {
    const meta = {};

    for (const property of objectNode.properties) {
      if (property.key.type === 'Identifier' && property.value.type === 'StringLiteral') {
        meta[property.key.name] = property.value.value;
      } else if (property.key.type === 'Identifier' && property.value.type === 'BooleanLiteral') {
        meta[property.key.name] = property.value.value;
      }
    }

    return meta;
  }

  /**
   * 使用 AI 解析路由
   */
  async parseWithAI() {
    try {
      for (const routerFile of this.routerFiles) {
        const content = await fs.readFile(routerFile.fullPath, 'utf8');
        const aiRoutes = await this.aiParseRoutes(content, routerFile.path);

        if (aiRoutes.length > 0) {
          // AI 解析的路由已经是扁平化的格式，直接添加
          this.routes.push(...aiRoutes);

          if (this.options.verbose) {
            console.log(chalk.green(`   AI 解析成功: ${routerFile.path} (${aiRoutes.length} 个路由)`));
          }
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  AI 解析失败: ${error.message}`));
    }
  }

  /**
   * AI 解析路由代码
   */
  async aiParseRoutes(content, filePath) {
    const prompt = `
你是一个 Vue Router 路由解析专家。请分析以下路由配置文件，提取所有可访问的路由路径。

文件路径: ${filePath}

代码内容:
\`\`\`javascript
${content}
\`\`\`

任务要求：
1. 分析路由配置，找出所有可访问的页面路径
2. 对于嵌套路由，展开为完整路径（如 /table + dynamic-table = /table/dynamic-table）
3. 跳过 redirect 路由、hidden: true 的路由、通配符路由（* 或 :pathMatch）
4. 保留动态路由参数（如 /user/:id）

请严格按照以下 JSON 格式返回结果，不要添加任何其他内容：

\`\`\`json
[
  {
    "path": "/dashboard",
    "name": "Dashboard",
    "meta": {}
  },
  {
    "path": "/table/dynamic-table",
    "name": "DynamicTable",
    "meta": {}
  }
]
\`\`\`

重要：只返回 JSON 数组，不要返回 JavaScript 代码或其他格式。
`;

    try {
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'route-parsing',
          fileName: filePath
        }
      });

      // 尝试解析 JSON 响应
      let jsonContent = null;

      // 首先尝试匹配 ```json 代码块
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      } else {
        // 如果没有代码块，尝试直接解析整个响应（去除前后空白）
        const trimmedResponse = response.trim();
        if (trimmedResponse.startsWith('[') && trimmedResponse.endsWith(']')) {
          jsonContent = trimmedResponse;
        }
      }

      if (jsonContent) {
        try {
          const routes = JSON.parse(jsonContent);
          if (Array.isArray(routes)) {
            // 将 AI 解析的路由转换为内部格式
            return this.normalizeAIRoutes(routes);
          }
        } catch (parseError) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`⚠️  JSON 解析失败 ${filePath}: ${parseError.message}`));
          }
        }
      } else {
        if (this.options.verbose) {
          console.log(chalk.yellow(`⚠️  未找到有效的 JSON 响应 ${filePath}`));
        }
      }

      return [];
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  AI 解析路由失败 ${filePath}: ${error.message}`));
      }
      return [];
    }
  }

  /**
   * 标准化 AI 解析的路由格式
   */
  normalizeAIRoutes(aiRoutes) {
    const normalizedRoutes = [];

    for (const route of aiRoutes) {
      if (route.path && !route.path.includes('*') && !route.path.includes(':pathMatch')) {
        normalizedRoutes.push({
          path: route.path,
          name: route.name || null,
          component: route.component || null,
          meta: route.meta || {},
          children: []
        });
      }
    }

    return normalizedRoutes;
  }

  /**
   * 获取所有可访问的路由路径
   */
  getAllRoutePaths() {
    const paths = [];

    const extractPaths = (routes, basePath = '') => {
      for (const route of routes) {
        if (route.path) {
          // 跳过隐藏路由
          if (route.hidden === true) {
            continue;
          }

          // 跳过重定向路由（除非它们有子路由）
          if (route.redirect && (!route.children || route.children.length === 0)) {
            continue;
          }

          let fullPath = route.path;

          // 处理相对路径
          if (!route.path.startsWith('/') && basePath) {
            fullPath = basePath + '/' + route.path;
          } else if (!route.path.startsWith('/')) {
            fullPath = '/' + route.path;
          }

          // 跳过通配符路由和参数匹配路由
          if (!fullPath.includes('*') &&
              !fullPath.includes(':pathMatch') &&
              !fullPath.includes('/redirect')) {

            // 如果有子路由，只添加有实际组件的路由
            if (route.children && route.children.length > 0) {
              // 对于有子路由的父路由，检查是否有实际的组件
              if (route.component && route.component.type !== 'unknown') {
                paths.push({
                  path: fullPath,
                  name: route.name,
                  meta: route.meta || {},
                  hasChildren: true
                });
              }
              // 递归处理子路由
              extractPaths(route.children, fullPath);
            } else {
              // 叶子路由，直接添加
              paths.push({
                path: fullPath,
                name: route.name,
                meta: route.meta || {},
                hasChildren: false
              });
            }
          }
        }
      }
    };

    extractPaths(this.routes);

    // 去重并排序
    const uniquePaths = [];
    const seenPaths = new Set();

    for (const pathInfo of paths) {
      if (!seenPaths.has(pathInfo.path)) {
        seenPaths.add(pathInfo.path);
        uniquePaths.push(pathInfo);
      }
    }

    return uniquePaths.sort((a, b) => a.path.localeCompare(b.path));
  }

  /**
   * 打印解析结果
   */
  printRoutes() {
    console.log(chalk.blue('\n📋 解析到的路由:'));

    const paths = this.getAllRoutePaths();
    for (const route of paths) {
      console.log(chalk.gray(`   ${route.path}${route.name ? ` (${route.name})` : ''}`));
    }
  }

  /**
   * 获取解析结果
   */
  getRoutes() {
    return this.routes;
  }

  /**
   * 获取所有路由路径
   */
  getRoutePaths() {
    return this.getAllRoutePaths();
  }

  /**
   * 建立路由到组件文件的映射
   */
  buildRouteComponentMapping(route, parentPath = '') {
    // 规范化路径连接，确保正确处理斜杠
    const fullPath = this.normalizePath(parentPath, route.path);

    if (route.component && route.component.source) {
      // 解析组件文件路径
      const componentPath = this.resolveComponentPath(route.component.source);
      if (componentPath) {
        this.routeComponentMap.set(fullPath, componentPath);
      }
    }

    // 递归处理子路由
    if (route.children && route.children.length > 0) {
      for (const child of route.children) {
        this.buildRouteComponentMapping(child, fullPath);
      }
    }
  }

  /**
   * 规范化路径，确保路径段之间只有一个斜杠
   * @param {string} parentPath - 父路径
   * @param {string} childPath - 子路径
   * @returns {string} - 规范化后的完整路径
   */
  normalizePath(parentPath, childPath) {
    // 处理空路径的情况
    if (!parentPath) {
      return childPath.startsWith('/') ? childPath : `/${childPath}`;
    }

    if (!childPath) {
      return parentPath;
    }

    // 处理父路径末尾和子路径开头的斜杠
    const hasParentSlash = parentPath.endsWith('/');
    const hasChildSlash = childPath.startsWith('/');

    // 根据不同情况处理路径连接
    if (hasParentSlash && hasChildSlash) {
      // 双斜杠情况，去掉一个
      return parentPath + childPath.substring(1);
    } else if (!hasParentSlash && !hasChildSlash) {
      // 无斜杠情况，添加一个
      return `${parentPath}/${childPath}`;
    } else {
      // 正好一个斜杠的情况
      return parentPath + childPath;
    }
  }

  /**
   * 解析组件文件路径
   */
  resolveComponentPath(componentSource) {
    if (!componentSource || typeof componentSource !== 'string') {
      return null;
    }

    // 处理相对路径
    let resolvedPath = componentSource;

    // 移除 @ 别名，替换为 src
    if (resolvedPath.startsWith('@/')) {
      resolvedPath = resolvedPath.replace('@/', 'src/');
    }

    // 如果是相对路径，基于当前路由文件解析
    if (resolvedPath.startsWith('./') || resolvedPath.startsWith('../')) {
      const currentDir = path.dirname(this.currentFilePath || 'src/router');
      resolvedPath = path.resolve(currentDir, resolvedPath);
      // 转换为相对于项目根目录的路径
      resolvedPath = path.relative(this.projectPath, resolvedPath);
    }

    // 添加 .vue 扩展名（如果没有）
    if (!resolvedPath.endsWith('.vue') && !resolvedPath.endsWith('.js') && !resolvedPath.endsWith('.ts')) {
      resolvedPath += '.vue';
    }

    return resolvedPath;
  }

  /**
   * 根据路由路径获取组件文件路径
   */
  getComponentPathByRoute(routePath) {
    return this.routeComponentMap.get(routePath);
  }

  /**
   * 获取所有路由组件映射
   */
  getRouteComponentMap() {
    return this.routeComponentMap;
  }

  /**
   * 根据错误信息中的路由路径，推断可能的组件文件路径
   */
  inferComponentPaths(routePath, errorMessage = '') {
    const possiblePaths = [];

    // 1. 首先检查直接映射
    const directPath = this.getComponentPathByRoute(routePath);
    if (directPath) {
      possiblePaths.push(directPath);
      if (this.options.verbose) {
        console.log(chalk.gray(`   🎯 找到路由映射: ${routePath} -> ${directPath}`));
      }
    }

    // 2. 检查父路由的映射（对于嵌套路由）
    const parentPaths = this.findParentRouteMappings(routePath);
    possiblePaths.push(...parentPaths);

    // 3. 基于路由路径推断
    const inferredPaths = this.inferPathsFromRoute(routePath);
    possiblePaths.push(...inferredPaths);

    // 4. 基于错误信息推断
    if (errorMessage) {
      const errorPaths = this.inferPathsFromError(errorMessage);
      possiblePaths.push(...errorPaths);
    }

    // 5. 检查实际文件是否存在
    const validPaths = this.filterExistingPaths(possiblePaths);

    // 去重并返回，优先返回存在的文件
    return [...new Set([...validPaths, ...possiblePaths])];
  }

  /**
   * 查找父路由的映射关系
   */
  findParentRouteMappings(routePath) {
    const paths = [];
    const segments = routePath.split('/').filter(Boolean);

    // 检查各级父路径
    for (let i = segments.length - 1; i > 0; i--) {
      const parentPath = '/' + segments.slice(0, i).join('/');
      const parentComponent = this.getComponentPathByRoute(parentPath);
      if (parentComponent) {
        paths.push(parentComponent);
      }
    }

    return paths;
  }

  /**
   * 过滤出实际存在的文件路径
   */
  filterExistingPaths(paths) {
    const validPaths = [];

    for (const filePath of paths) {
      try {
        const fullPath = path.resolve(this.projectPath, filePath);
        if (fs.existsSync(fullPath)) {
          validPaths.push(filePath);
        }
      } catch (error) {
        // 忽略文件检查错误
      }
    }

    return validPaths;
  }

  /**
   * 基于路由路径推断组件路径
   */
  inferPathsFromRoute(routePath) {
    const paths = [];

    // 移除开头的斜杠
    const cleanPath = routePath.replace(/^\/+/, '');

    if (!cleanPath) {
      // 根路径的可能位置
      paths.push('src/views/dashboard/index.vue');
      paths.push('src/views/home/<USER>');
      return paths;
    }

    // 分割路径
    const segments = cleanPath.split('/');

    // 常见的组件路径模式
    const patterns = [
      // views 目录
      `src/views/${segments.join('/')}.vue`,
      `src/views/${segments.join('/')}/index.vue`,
      `src/views/${segments[0]}/${segments.slice(1).join('-')}.vue`,

      // components 目录
      `src/components/${segments.join('/')}.vue`,
      `src/components/${segments.join('/')}/index.vue`,

      // 首字母大写的组件名
      `src/views/${segments.map(s => s.charAt(0).toUpperCase() + s.slice(1)).join('/')}.vue`,
      `src/components/${segments.map(s => s.charAt(0).toUpperCase() + s.slice(1)).join('/')}.vue`,
    ];

    paths.push(...patterns);
    return paths;
  }

  /**
   * 基于错误信息推断组件路径
   */
  inferPathsFromError(errorMessage) {
    const paths = [];

    // 从错误信息中提取可能的组件名
    const componentMatches = errorMessage.match(/(\w+)Chart|(\w+)Component|(\w+)Demo/g);
    if (componentMatches) {
      for (const match of componentMatches) {
        paths.push(`src/components/${match}.vue`);
        paths.push(`src/components/Charts/${match}.vue`);
        paths.push(`src/views/${match.toLowerCase()}/index.vue`);
      }
    }

    return paths;
  }
}

module.exports = RouteParser;
