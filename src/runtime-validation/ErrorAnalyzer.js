const chalk = require('chalk');

/**
 * ErrorAnalyzer - 错误分析器
 * 
 * 功能：
 * 1. 错误检测和收集
 * 2. 错误过滤和验证
 * 3. 错误格式化和显示
 * 4. 错误分类和去重
 */
class ErrorAnalyzer {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      ...options
    };
  }

  /**
   * 判断是否是有效的代码错误（过滤掉非代码错误）
   */
  isValidCodeError(errorMessage) {
    if (!errorMessage || typeof errorMessage !== 'string') {
      return false;
    }

    const message = errorMessage.toLowerCase().split("\n")[0];

    // 过滤掉空的或无意义的错误信息
    if (message === '{}' || message === '[object object]' || message.trim() === '') {
      return false;
    }

    // 过滤掉非代码错误
    const nonCodeErrorPatterns = [
      /network error/i,
      /failed to load resource/i,
      /loading chunk \d+ failed/i,
      /loading css chunk \d+ failed/i,
      /net::/i,
      /http error/i,
      /cors error/i,
      /connection refused/i,
      /dns_probe_finished_nxdomain/i,
      /err_internet_disconnected/i,
      /login/i,
      /permission/i,
      /unauthorized/i,
      /forbidden/i,
      /not found/i,
      /404/i,
      /500/i,
      /502/i,
      /503/i,
      /504/i,
      // 移除过于宽泛的 timeout 模式，只过滤网络相关的超时
      /navigation timeout/i,
      /request timeout/i,
      /connection timeout/i
    ];

    // 如果匹配非代码错误模式，则过滤掉
    for (const pattern of nonCodeErrorPatterns) {
      if (pattern.test(message)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    🔍 过滤非代码错误: "${errorMessage.substring(0, 100)}..." (匹配模式: ${pattern})`));
        }
        return false;
      }
    }

    // 优先检查真正的代码错误模式
    const codeErrorPatterns = [
      /cannot read propert/i,
      /cannot access before initialization/i,
      /is not defined/i,
      /is not a function/i,
      /is not a constructor/i,
      /unexpected token/i,
      /syntax error/i,
      /reference error/i,
      /type error/i,
      /range error/i,
      /eval error/i,
      /uri error/i,
      /uncaught/i,
      /vue.*error/i,
      /component.*error/i,
      /render.*error/i,
      /computed.*error/i,
      /watch.*error/i,
      /lifecycle.*error/i,
      // 添加更多常见的运行时错误模式
      /cannot read properties of/i,
      /cannot set properties of/i,
      /null is not an object/i,
      /undefined is not an object/i,
      /object is not a function/i,
      /property.*does not exist/i,
      /failed to execute/i,
      /invalid.*argument/i,
      /maximum call stack/i,
      /stack overflow/i
    ];

    // 检查是否匹配代码错误模式
    for (const pattern of codeErrorPatterns) {
      if (pattern.test(message)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    ✅ 识别为代码错误: "${errorMessage.substring(0, 100)}..." (匹配模式: ${pattern})`));
        }
        return true;
      }
    }

    // 对于包含 "error" 但不包含 "warning" 的消息，进行更宽松的判断
    if (message.includes('error') && !message.includes('warning')) {
      // 排除一些明显的非代码错误
      const excludePatterns = [
        /screenshot/i,
        /image/i,
        /video/i,
        /audio/i,
        /font/i,
        /css/i,
        /style/i
      ];

      for (const pattern of excludePatterns) {
        if (pattern.test(message)) {
          if (this.options.verbose) {
            console.log(chalk.gray(`    🔍 过滤资源错误: "${errorMessage.substring(0, 100)}..." (匹配模式: ${pattern})`));
          }
          return false;
        }
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    ⚠️  默认识别为代码错误: "${errorMessage.substring(0, 100)}..."`));
      }
      return true;
    }

    if (this.options.verbose) {
      console.log(chalk.gray(`    ❌ 不识别为代码错误: "${errorMessage.substring(0, 100)}..."`));
    }
    return false;
  }

  /**
   * 检查是否是重复错误
   */
  isDuplicateError(existingErrors, newErrorMessage) {
    if (!Array.isArray(existingErrors) || !newErrorMessage) {
      return false;
    }

    // 提取错误的核心信息进行比较
    const normalizeError = (error) => {
      const errorStr = typeof error === 'string' ? error : (error.message || String(error));
      return errorStr
        .replace(/Console Error: |Page Error: |Vue Error: /g, '')
        .replace(/\s+/g, ' ')
        .trim()
        .toLowerCase();
    };

    const normalizedNew = normalizeError(newErrorMessage);

    // 检查是否已存在相同的错误
    for (const existingError of existingErrors) {
      const normalizedExisting = normalizeError(existingError);
      if (normalizedExisting === normalizedNew) {
        return true;
      }
    }

    return false;
  }

  /**
   * 过滤出可以修复的错误（排除登录、权限、网络等非代码错误）
   */
  filterFixableErrors(errors) {
    const unfixablePatterns = [
      // 登录相关错误
      /登录/,
      /权限/,
      /authentication/i,
      /permission/i,
      /unauthorized/i,

      // 网络相关错误
      /network error/i,
      /http \d+/i,
      /failed to fetch/i,
      /connection/i,
      /cors/i,

      // 页面访问相关错误
      /页面访问失败/,
      /被重定向/,
      /跳过了自动登录/,
      /navigation timeout/i,

      // 非关键错误
      /截图失败/,
      /登录检查失败/,
      /screenshot/i,

      // 资源加载错误（通常不是代码问题）
      /failed to load resource/i,
      /loading chunk.*failed/i,
      /loading css chunk.*failed/i,

      // 警告信息（不是错误）
      /console warning/i,
      /vue warning/i,
      /warning:/i
    ];

    // 可修复的错误模式（优先级更高）
    const fixablePatterns = [
      /cannot read propert/i,
      /cannot access before initialization/i,
      /is not defined/i,
      /is not a function/i,
      /is not a constructor/i,
      /unexpected token/i,
      /syntax error/i,
      /reference error/i,
      /type error/i,
      /uncaught/i,
      /vue.*error/i,
      /component.*error/i,
      /render.*error/i,
      /cannot read properties of/i,
      /cannot set properties of/i,
      /null is not an object/i,
      /undefined is not an object/i
    ];

    const fixableErrors = errors.filter(error => {
      const errorMessage = typeof error === 'string' ? error : (error.message || JSON.stringify(error));

      // 首先检查是否是明确可修复的错误
      const isExplicitlyFixable = fixablePatterns.some(pattern => pattern.test(errorMessage));

      if (isExplicitlyFixable) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    ✅ 明确可修复错误: "${errorMessage.substring(0, 100)}..."`));
        }
        return true;
      }

      // 然后检查是否匹配不可修复的模式
      const isUnfixable = unfixablePatterns.some(pattern => pattern.test(errorMessage));

      if (isUnfixable) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    ❌ 不可修复错误: "${errorMessage.substring(0, 100)}..."`));
        }
        return false;
      }

      // 对于其他包含 "error" 的消息，默认认为可修复
      const containsError = errorMessage.toLowerCase().includes('error') &&
                           !errorMessage.toLowerCase().includes('warning');

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 默认判断 (包含error: ${containsError}): "${errorMessage.substring(0, 100)}..."`));
      }

      return containsError;
    });

    // 添加过滤结果的详细日志
    console.log(chalk.gray(`    📊 错误过滤结果: 总错误 ${errors.length} 个，可修复 ${fixableErrors.length} 个`));

    if (this.options.verbose && fixableErrors.length > 0) {
      console.log(chalk.gray(`    📝 可修复的错误:`));
      fixableErrors.slice(0, 3).forEach((error, index) => {
        const errorMessage = typeof error === 'string' ? error : (error.message || JSON.stringify(error));
        console.log(chalk.gray(`      ${index + 1}. ${errorMessage.substring(0, 150)}...`));
      });
      if (fixableErrors.length > 3) {
        console.log(chalk.gray(`      ... 还有 ${fixableErrors.length - 3} 个可修复错误`));
      }
    }

    return fixableErrors;
  }

  /**
   * 显示错误摘要信息
   */
  displayErrorSummary(errors, maxDisplay = 3) {
    if (!errors || errors.length === 0) return;

    console.log(chalk.gray(`    📋 错误详情 (显示前 ${Math.min(maxDisplay, errors.length)} 个):`));

    errors.slice(0, maxDisplay).forEach((error, index) => {
      let errorMessage = '';
      let errorType = 'Unknown';

      if (typeof error === 'string') {
        errorMessage = error;
        // 尝试从错误消息中提取类型
        if (error.includes('Console Error:')) {
          errorType = 'Console Error';
          errorMessage = error.replace('Console Error: ', '');
        } else if (error.includes('Page Error:')) {
          errorType = 'Page Error';
          errorMessage = error.replace('Page Error: ', '');
        } else if (error.includes('Vue Error:')) {
          errorType = 'Vue Error';
          errorMessage = error.replace('Vue Error: ', '');
        }
      } else if (error && typeof error === 'object') {
        errorType = error.type || 'Object Error';
        errorMessage = error.message || JSON.stringify(error);
      } else {
        errorMessage = String(error);
      }

      // 截断过长的错误信息，但保留足够的信息用于调试
      const truncatedMessage = errorMessage.length > 300
        ? errorMessage.substring(0, 300) + '...'
        : errorMessage;

      // 使用不同颜色显示不同类型的错误
      let coloredType = chalk.red(errorType);
      if (errorType.includes('Console')) {
        coloredType = chalk.yellow(errorType);
      } else if (errorType.includes('Vue')) {
        coloredType = chalk.magenta(errorType);
      }

      console.log(chalk.gray(`      ${index + 1}. ${coloredType}: ${truncatedMessage}`));

      // 如果是对象错误且有额外信息，显示更多详情
      if (typeof error === 'object' && error.details && this.options.verbose) {
        console.log(chalk.gray(`         详情: ${JSON.stringify(error.details, null, 2).substring(0, 200)}...`));
      }
    });

    // 如果还有更多错误，显示省略信息
    if (errors.length > maxDisplay) {
      console.log(chalk.gray(`      ... 还有 ${errors.length - maxDisplay} 个错误`));

      // 显示剩余错误的类型统计
      const remainingErrors = errors.slice(maxDisplay);
      const errorTypeCounts = new Map();

      remainingErrors.forEach(error => {
        let type = 'Unknown';
        if (typeof error === 'string') {
          if (error.includes('Console Error:')) type = 'Console Error';
          else if (error.includes('Page Error:')) type = 'Page Error';
          else if (error.includes('Vue Error:')) type = 'Vue Error';
          else type = 'Other Error';
        } else if (error && typeof error === 'object') {
          type = error.type || 'Object Error';
        }

        errorTypeCounts.set(type, (errorTypeCounts.get(type) || 0) + 1);
      });

      if (errorTypeCounts.size > 0) {
        console.log(chalk.gray(`      类型统计: ${Array.from(errorTypeCounts.entries()).map(([type, count]) => `${type}(${count})`).join(', ')}`));
      }
    }
  }

  /**
   * 格式化错误信息用于显示
   */
  formatErrorsForDisplay(errors, maxErrors = 3) {
    if (!Array.isArray(errors) || errors.length === 0) {
      return [];
    }

    // 对错误进行分类和去重
    const errorGroups = new Map();

    for (const error of errors) {
      const errorInfo = this.parseErrorInfo(error);
      const key = errorInfo.type + ':' + errorInfo.coreMessage;

      if (!errorGroups.has(key)) {
        errorGroups.set(key, {
          ...errorInfo,
          count: 0,
          examples: []
        });
      }

      const group = errorGroups.get(key);
      group.count++;
      if (group.examples.length < 2) {
        group.examples.push(errorInfo.originalMessage);
      }
    }

    // 按重要性和出现频率排序
    const sortedGroups = Array.from(errorGroups.values())
      .sort((a, b) => {
        // 首先按错误类型的重要性排序
        const priorityA = this.getErrorPriority(a.type);
        const priorityB = this.getErrorPriority(b.type);
        if (priorityA !== priorityB) {
          return priorityA - priorityB; // 数字越小优先级越高
        }
        // 然后按出现频率排序
        return b.count - a.count;
      });

    // 格式化输出
    return sortedGroups.slice(0, maxErrors).map(group => {
      let summary = group.coreMessage;
      if (group.count > 1) {
        summary += ` (出现 ${group.count} 次)`;
      }

      return {
        type: group.type,
        summary: summary,
        details: group.details,
        count: group.count,
        examples: group.examples
      };
    });
  }

  /**
   * 解析错误信息
   */
  parseErrorInfo(error) {
    const originalMessage = typeof error === 'string' ? error : (error.message || JSON.stringify(error));

    // 提取错误类型
    let type = 'Unknown Error';
    let coreMessage = originalMessage;
    let details = null;

    if (originalMessage.includes('Console Error:')) {
      type = 'Console Error';
      coreMessage = originalMessage.replace('Console Error: ', '');
    } else if (originalMessage.includes('Page Error:')) {
      type = 'Page Error';
      coreMessage = originalMessage.replace('Page Error: ', '');
    } else if (originalMessage.includes('Vue Error:')) {
      type = 'Vue Error';
      coreMessage = originalMessage.replace('Vue Error: ', '');
    } else if (originalMessage.includes('Network Error:')) {
      type = 'Network Error';
      coreMessage = originalMessage.replace('Network Error: ', '');
    }

    // 提取核心错误信息（去掉具体的变量名等）
    coreMessage = this.extractCoreErrorMessage(coreMessage);

    // 如果消息太长，截取并保存详情
    if (coreMessage.length > 100) {
      details = coreMessage;
      coreMessage = coreMessage.substring(0, 100) + '...';
    }

    return {
      type,
      coreMessage,
      details,
      originalMessage
    };
  }

  /**
   * 提取核心错误信息
   */
  extractCoreErrorMessage(message) {
    // 移除具体的变量名、文件路径等，保留错误模式
    let coreMessage = message
      .replace(/at\s+.*?\s+\(.*?\)/g, '') // 移除堆栈跟踪
      .replace(/\s+at\s+.*$/g, '') // 移除行尾的堆栈信息
      .replace(/\s+\(.*?\)$/g, '') // 移除括号中的位置信息
      .replace(/line\s+\d+/gi, 'line X') // 替换具体行号
      .replace(/column\s+\d+/gi, 'column X') // 替换具体列号
      .replace(/\d+:\d+/g, 'X:X') // 替换行:列格式
      .trim();

    return coreMessage;
  }

  /**
   * 获取错误类型的优先级（数字越小优先级越高）
   */
  getErrorPriority(errorType) {
    const priorities = {
      'Page Error': 1,      // 页面错误最重要
      'Vue Error': 2,       // Vue 错误次之
      'Console Error': 3,   // 控制台错误
      'Network Error': 4,   // 网络错误
      'Unknown Error': 5    // 未知错误优先级最低
    };

    return priorities[errorType] || 5;
  }
}

module.exports = ErrorAnalyzer;
