const path = require('path');
const fs = require('fs-extra');
const { contextManager, ContextManager } = require('../src/core/ContextManager');
const MigrationContext = require('../src/core/MigrationContext');
const ContextAwareComponent = require('../src/core/ContextAwareComponent');

describe('Context System', () => {
  let testProjectPath;
  let contextId;
  let context;

  beforeEach(async () => {
    // 创建临时测试项目
    testProjectPath = path.join(__dirname, 'temp-test-project');
    await fs.ensureDir(testProjectPath);
    
    // 创建基本的 package.json
    await fs.writeJson(path.join(testProjectPath, 'package.json'), {
      name: 'test-project',
      version: '1.0.0',
      dependencies: {
        vue: '^2.6.14'
      }
    });
  });

  afterEach(async () => {
    // 清理测试项目
    if (contextId) {
      contextManager.removeContext(contextId);
    }
    await fs.remove(testProjectPath);
  });

  describe('MigrationContext', () => {
    it('should create a new migration context', () => {
      const context = new MigrationContext(testProjectPath, { mode: 'test' });

      expect(context.projectPath).toBe(testProjectPath);
      expect(context.options.mode).toBe('test');
      expect(context.project.path).toBe(testProjectPath);
      expect(context.phases.current).toBeNull();
      expect(context.phases.completed).toEqual([]);
    });

    it('should manage phases correctly', () => {
      const context = new MigrationContext(testProjectPath);

      // 开始阶段
      context.startPhase('test-phase');
      expect(context.phases.current).toBe('test-phase');

      // 完成阶段
      const result = { success: true };
      context.completePhase('test-phase', result);
      expect(context.phases.current).toBeNull();
      expect(context.phases.completed).toContain('test-phase');
      expect(context.phases.results['test-phase']).toEqual(result);
    });

    it('should track errors and warnings', () => {
      const context = new MigrationContext(testProjectPath);
      
      const error = new Error('Test error');
      const warning = 'Test warning';
      
      context.addError(error, 'test-context');
      context.addWarning(warning, 'test-context');
      
      expect(context.issues.errors).toHaveLength(1);
      expect(context.issues.warnings).toHaveLength(1);
      expect(context.issues.errors[0].message).toBe('Test error');
      expect(context.issues.warnings[0].message).toBe('Test warning');
    });

    it('should export and import context data', async () => {
      const context = new MigrationContext(testProjectPath);
      
      // 设置一些数据
      context.setProjectInfo({ name: 'test', type: 'vue' });
      context.setConfig('testKey', 'testValue');
      context.addError(new Error('Test error'));
      
      // 导出数据
      const exportedData = context.export();
      
      expect(exportedData.project.name).toBe('test');
      expect(exportedData.config.testKey).toBe('testValue');
      expect(exportedData.issues.errors).toHaveLength(1);
    });
  });

  describe('ContextManager', () => {
    it('should create and manage contexts', () => {
      const result = contextManager.createContext(testProjectPath, { mode: 'test' });
      
      contextId = result.contextId;
      context = result.context;
      
      expect(typeof contextId).toBe('string');
      expect(context).toBeInstanceOf(MigrationContext);
      expect(contextManager.getContext(contextId)).toBe(context);
    });

    it('should handle multiple contexts', () => {
      const result1 = contextManager.createContext(testProjectPath, { mode: 'test1' });
      const result2 = contextManager.createContext(testProjectPath, { mode: 'test2' });
      
      expect(result1.contextId).not.toBe(result2.contextId);
      expect(contextManager.getContext(result1.contextId)).toBe(result1.context);
      expect(contextManager.getContext(result2.contextId)).toBe(result2.context);
      
      // 清理
      contextManager.removeContext(result1.contextId);
      contextManager.removeContext(result2.contextId);
    });

    it('should manage event subscriptions', (done) => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      let eventReceived = false;
      
      // 订阅事件
      contextManager.subscribe('phase:start', (data) => {
        expect(data.contextId).toBe(contextId);
        expect(data.phase).toBe('test-phase');
        eventReceived = true;
        done();
      }, contextId);
      
      // 触发事件 - 确保事件确实被触发了
      setTimeout(() => {
        context.startPhase('test-phase');
      }, 100); // 添加一点延迟确保订阅已完成
    });

    it('should provide health check functionality', () => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      // 添加一些错误
      context.addError(new Error('Critical error'), 'critical-test');
      
      const health = contextManager.healthCheck();
      
      expect(health.status).not.toBe('healthy');
      expect(health.contexts[contextId].status).toBe('critical');
    });
  });

  describe('ContextAwareComponent', () => {
    class TestComponent extends ContextAwareComponent {
      constructor(options = {}) {
        super('TestComponent', options);
        this.executed = false;
      }

      async execute() {
        this.executed = true;
        return { success: true };
      }
    }

    it('should initialize with context', async () => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      const component = new TestComponent();
      await component.initialize(contextId);
      
      expect(component.isInitialized).toBe(true);
      expect(component.getContext()).toBe(context);
      expect(context.getTool('TestComponent')).toBe(component);
    });

    it('should execute with context awareness', async () => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      const component = new TestComponent();
      await component.initialize(contextId);
      
      const executeResult = await component.start();
      
      expect(component.executed).toBe(true);
      expect(component.state.status).toBe('completed');
      expect(executeResult.success).toBe(true);
    });

    it('should handle component dependencies', async () => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      const component1 = new TestComponent();
      const component2 = new TestComponent();
      
      await component1.initialize(contextId);
      await component2.initialize(contextId);
      
      // 模拟 component1 完成
      await component1.start();
      
      // component2 应该能够等待 component1
      const canProceed = await component2.waitForDependencies(['TestComponent'], 1000);
      expect(canProceed).toBe(true);
    });

    it('should publish and subscribe to events', (done) => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      const component = new TestComponent();
      component.initialize(contextId).then(() => {
        // 订阅自定义事件
        component.subscribe('custom:event', (data) => {
          expect(data.message).toBe('test message');
          done();
        });
        
        // 发布事件 - 增加延时确保订阅已完成
        setTimeout(() => {
          component.publish('custom:event', { message: 'test message' });
        }, 100);
      });
    });

    it('should handle errors properly', async () => {
      const result = contextManager.createContext(testProjectPath);
      contextId = result.contextId;
      context = result.context;
      
      class FailingComponent extends ContextAwareComponent {
        constructor() {
          super('FailingComponent');
        }
        
        async execute() {
          throw new Error('Component failed');
        }
      }
      
      const component = new FailingComponent();
      await component.initialize(contextId);
      
      try {
        await component.start();
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).toBe('Component failed');
        expect(component.state.status).toBe('failed');
        expect(component.state.error).toBe(error);
      }
    });
  });

  describe('Integration', () => {
    it('should handle complete migration workflow', async () => {
      const result = contextManager.createContext(testProjectPath, {
        mode: 'integration-test'
      });
      contextId = result.contextId;
      context = result.context;
      
      // 设置项目信息
      context.setProjectInfo({
        name: 'integration-test',
        type: 'vue',
        detectedFramework: 'Vue 2'
      });
      
      // 创建测试组件
      class AnalysisComponent extends ContextAwareComponent {
        constructor() { super('AnalysisComponent'); }
        async execute() { return { filesAnalyzed: 10 }; }
      }
      
      class MigrationComponent extends ContextAwareComponent {
        constructor() { super('MigrationComponent'); }
        async execute() { return { filesMigrated: 8 }; }
      }
      
      const analyzer = new AnalysisComponent();
      const migrator = new MigrationComponent();
      
      await analyzer.initialize(contextId);
      await migrator.initialize(contextId);
      
      // 执行工作流
      context.startPhase('analysis');
      const analysisResult = await analyzer.start();
      context.completePhase('analysis', analysisResult);
      
      context.startPhase('migration');
      const migrationResult = await migrator.start();
      context.completePhase('migration', migrationResult);
      
      context.completeMigration();
      
      // 验证结果
      const summary = context.getStatusSummary();
      expect(summary.completedPhases).toBe(2);
      expect(context.phases.results.analysis.filesAnalyzed).toBe(10);
      expect(context.phases.results.migration.filesMigrated).toBe(8);
    });
  });
});
